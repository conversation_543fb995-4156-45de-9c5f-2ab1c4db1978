<template>
  <div class="home-view">
    <!-- 顶部Header -->
    <Header />

    <!-- 侧边栏导航 -->
    <SidebarNavigation :current-slide="currentSlide" :active-tab="activeTab" :main-swiper-instance="mainSwiperInstance"
      @slide-change="onSlideChange" @tab-change="switchTab" />

    <!-- 主要垂直滚屏容器 -->
    <VerticalSwiper custom-class="main-swiper" @slide-change="onSlideChange" @swiper-ready="onMainSwiperReady">
      <!-- 第1屏 - 首页 -->
      <swiper-slide class="slide-item slide-1">
        <div class="slide-content">

          <!-- 预约按钮 -->
          <div class="reservation-btn">
            <WebM class="store-button breathe" :src="WebMReservation" @click="scrollToNextSlide" />
          </div>

          <!-- 居中的应用商店按钮组 -->
          <div class="app-store-buttons">
            <WebM class="store-button breathe" :src="WebMGooglePlay" @click="handleStoreClick('google')" />
            <WebM class="store-button breathe" :src="WebMAppleStore" @click="handleStoreClick('apple')" />
            <WebM class="store-button breathe" :src="WebMOneStore" @click="handleStoreClick('one')" />
            <WebM class="store-button breathe" :src="WebMSamsungStore" @click="handleStoreClick('samsung')" />
          </div>

          <!-- 底部滚动按钮 -->
          <div class="scroll-down-wrapper">
            <img src="@/assets/imgs/p1/scroll-down.png" alt="向下滚动" class="scroll-down-button bounce"
              @click="scrollToNextSlide" />
          </div>
        </div>
      </swiper-slide>

      <!-- 第2屏 - 预约 -->
      <swiper-slide class="slide-item slide-2">
        <WebM class="particle" :src="WebMParticle" />
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p2/title.png" alt="">
          </div>
          <div class="content">
            <!-- 预约奖励 -->
            <div class="rewards">
              <WebM class="reward" :src="WebMReservationReward1" />
              <WebM class="reward" :src="WebMReservationReward2" />
            </div>
            <!-- 平台选择按钮 -->
            <div class="platform-buttons">
              <button class="platform-btn aos-btn" :class="{ active: selectedPlatform === 'aos' }"
                @click="selectedPlatform = 'aos'">
              </button>
              <button class="platform-btn ios-btn" :class="{ active: selectedPlatform === 'ios' }"
                @click="selectedPlatform = 'ios'">
              </button>
            </div>
            <div class="phone-input">
              <PhoneInput v-model="phoneNumber" />
            </div>
            <div class="rule-btn">
              <img src="@/assets/imgs/p2/rule-btn.png" alt="">
            </div>
            <div class="reservation-btn">
              <WebM class="reservation breathe" :src="WebMP2ReservationBtn" />
            </div>
            <div class="big-reward">
              <WebM class="reward" :src="WebMReservationReward3" />
            </div>
            <div class="store-buttons">
              <WebM class="store-button breathe" :src="WebMGooglePlay" @click="handleStoreClick('google')" />
              <WebM class="store-button breathe" :src="WebMAppleStore" @click="handleStoreClick('apple')" />
            </div>
            <div class="community-btn">
              <WebM class="community breathe" :src="WebMP2CommunityBtn" />
            </div>
          </div>
        </div>
      </swiper-slide>

      <!-- 第3屏 - 抽奖 -->
      <swiper-slide class="slide-item slide-3">
        <WebM class="particle" :src="WebMParticle" />
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p3/title.png" alt="">
          </div>
          <div class="lottery">
            <WebM :src="WebMLottery" />
            <div class="lottery-btn">
              <img class="breathe" src="@/assets/imgs/p3/lottery-btn.png" alt="">
            </div>
          </div>
          <div class="rule-btn">
            <img src="@/assets/imgs/p3/rule-btn.png" alt="">
          </div>
        </div>
      </swiper-slide>

      <!-- 第4屏 - 里程碑 -->
      <swiper-slide class="slide-item slide-4">
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p4/title.png">
          </div>

          <div class="reservation-count">
            <img src="@/assets/imgs/p4/reservation-count.png" alt="">
            <div class="count">{{ reservationCount }}</div>
          </div>

          <div class="milestone-container">
            <!-- 动态里程碑图片 -->
            <div class="milestone-image">
              <img :src="milestoneImagesList[currentLevel]" :alt="`里程碑等级 ${currentLevel}`">
              <!-- 奖品图片 -->
              <div v-if="currentLevel === 5" class="prize-image shine">
                <img class="breathe" src="@/assets/imgs/p4/prize.png" alt="奖品">
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>

      <!-- 第5屏 - 职业介绍 -->
      <swiper-slide class="slide-item slide-5">
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p5/title.png">
          </div>
        </div>
      </swiper-slide>

      <!-- 第6屏 - 公会 -->
      <swiper-slide class="slide-item slide-6">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第7屏 - 游戏世界观介绍 -->
      <swiper-slide class="slide-item slide-7">
        <div class="slide-content world-view-container">

          <!-- Tab 切换导航 -->
          <div class="tab-navigation">
            <button :class="['tab-btn', 'tab1', { active: activeTab === 'tab1' }]" @click="switchTab('tab1')">
            </button>
            <button :class="['tab-btn', 'tab2', { active: activeTab === 'tab2' }]" @click="switchTab('tab2')">
            </button>
          </div>

          <!-- Tab 内容区域 -->
          <div class="tab-content">
            <!-- Tab Swiper: 使用fade效果的全屏切换 -->
            <Swiper :modules="[EffectFade]" effect="fade" :fade-effect="{ crossFade: true }" :allow-touch-move="false"
              :navigation="false" :pagination="false" :scrollbar="false" class="tab-swiper" @swiper="onTabSwiperReady"
              @slide-change="onTabSwiperChange">
              <!-- Tab1: 水平全屏轮播 -->
              <swiper-slide class="tab-panel tab1-panel">
                <WorldViewSwiper />
              </swiper-slide>

              <!-- Tab2: 固定背景 + 居中轮播 -->
              <swiper-slide class="tab-panel tab2-panel">
                <StorySwiper />
              </swiper-slide>
            </Swiper>
          </div>
        </div>
      </swiper-slide>
    </VerticalSwiper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { EffectFade } from 'swiper/modules'
// 导入自定义组件
import WebM from '@/components/WebM.vue'
import Header from '@/components/Header.vue'
import PhoneInput from '@/components/PhoneInput.vue'
import VerticalSwiper from '@/components/swiper/VerticalSwiper.vue'
import WorldViewSwiper from '@/components/swiper/WorldViewSwiper.vue'
import StorySwiper from '@/components/swiper/StorySwiper.vue'
import SidebarNavigation from '@/components/SidebarNavigation.vue'
// WebM
import WebMParticle from '@/assets/imgs/webm/particle.webm'
import WebMReservation from '@/assets/imgs/webm/reservation.webm'
import WebMAppleStore from '@/assets/imgs/webm/apple-store.webm'
import WebMGooglePlay from '@/assets/imgs/webm/google-play.webm'
import WebMOneStore from '@/assets/imgs/webm/one-store.webm'
import WebMSamsungStore from '@/assets/imgs/webm/samsung-store.webm'
import WebMP2ReservationBtn from '@/assets/imgs/webm/p2-reservation-btn.webm'
import WebMP2CommunityBtn from '@/assets/imgs/webm/p2-community-btn.webm'
import WebMReservationReward1 from '@/assets/imgs/webm/reservation-reward1.webm'
import WebMReservationReward2 from '@/assets/imgs/webm/reservation-reward2.webm'
import WebMReservationReward3 from '@/assets/imgs/webm/reservation-reward3.webm'
import WebMLottery from '@/assets/imgs/webm/lottery.webm'

// Hooks
import { useDeviceDetection } from '@/hooks/useDeviceDetection'

// 批量导入里程碑图片
const milestoneImagesRaw = import.meta.glob('@/assets/imgs/p4/lv*.png', { eager: true })
const milestoneImagesList = computed(() => {
  return Object.values(milestoneImagesRaw).map((item: any) => item.default)
})

// 响应式数据
const currentSlide = ref(0)
const activeTab = ref('tab1')
const mainSwiperInstance = ref<any>(null)
const tabSwiperInstance = ref<any>(null)

// 设备检测
const { deviceType } = useDeviceDetection()

// 平台选择 - 根据设备类型初始化
const selectedPlatform = ref<'aos' | 'ios'>(deviceType.value === 'ios' ? 'ios' : 'aos')

// 电话号码相关
const phoneNumber = ref('')

// 第4屏里程碑等级控制
const currentLevel = ref(0) // 0-5 对应 lv0.png 到 lv5.png
const reservationCount = ref(123456)

// 主 Swiper 事件处理
const onSlideChange = (index: number) => {
  currentSlide.value = index
}

const onMainSwiperReady = (swiper: any) => {
  mainSwiperInstance.value = swiper
}

// Tab 切换功能
const switchTab = (tab: string) => {
  activeTab.value = tab

  // 控制tab swiper切换
  if (tabSwiperInstance.value) {
    const slideIndex = tab === 'tab1' ? 0 : 1
    tabSwiperInstance.value.slideTo(slideIndex)
  }
}

// Tab Swiper 事件处理
const onTabSwiperReady = (swiper: any) => {
  tabSwiperInstance.value = swiper
}

const onTabSwiperChange = (swiper: any) => {
  const currentIndex = swiper.activeIndex
  activeTab.value = currentIndex === 0 ? 'tab1' : 'tab2'
}

// 应用商店按钮点击处理
const handleStoreClick = (store: string) => {
  console.log(`点击了${store}应用商店`)
}

// 滚动到下一屏
const scrollToNextSlide = () => {
  if (mainSwiperInstance.value) {
    mainSwiperInstance.value.slideTo(1) // 导航到第2屏
  }
}
</script>

<style lang="less" scoped>
.home-view {
  width: 100%;
  height: 100vh;
  min-width: 1200px;
  overflow: hidden;
  position: relative;
}

// 粒子效果
.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.slide-item {
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  .title {
    position: absolute;
    top: 10px;
  }

  // 为每个屏幕设置背景图
  &.slide-1 {
    background-image: url('@/assets/imgs/bg/p1.jpg');
  }

  &.slide-2 {
    background-image: url('@/assets/imgs/bg/p2.jpg');
  }

  &.slide-3 {
    background-image: url('@/assets/imgs/bg/p3.jpg');
  }

  &.slide-4 {
    background-image: url('@/assets/imgs/bg/p4.jpg');
  }

  &.slide-5 {
    background-image: url('@/assets/imgs/bg/p5.jpg');
  }

  &.slide-6 {
    background-image: url('@/assets/imgs/bg/p6.jpg');
  }

  &.slide-7 {
    background-color: #000;
  }
}

.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 第1屏
.slide-1 {
  .reservation-btn {
    position: absolute;
    right: 24%;
    bottom: 24%;
    cursor: pointer;
  }

  .app-store-buttons {
    position: absolute;
    bottom: 130px;
    left: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    transform: translateX(-50%);

    .store-button {
      width: 210px;
      height: 65px;
      cursor: pointer;
    }

    @media (max-width: 480px) {}
  }

  // 滚动按钮样式
  .scroll-down-wrapper {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);

    .scroll-down-button {
      width: 121px;
      height: auto;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        filter: drop-shadow(0 4px 8px rgba(255, 255, 255, 0.3));
      }
    }

    @media (max-width: 768px) {
      bottom: 30px;

      .scroll-down-button {
        width: 50px;
      }
    }
  }
}

// 第2屏
.slide-2 {

  .title {
    position: absolute;
    top: 10px;
  }

  .content {
    position: absolute;
    top: 170px;
    margin-left: -50px;
    width: 1239px;
    height: 776px;
    background: url(@/assets/imgs/p2/box-bg.png);
    display: flex;
    align-items: center;
    justify-content: center;

    .rewards {
      position: absolute;
      top: 130px;
      left: 100px;
      display: flex;
      align-items: center;

      .reward {
        margin: 0 -85px;
      }
    }

    .platform-buttons {
      position: absolute;
      top: 470px;
      left: 290px;
      display: flex;
      gap: 80px;
      align-items: center;
      justify-content: center;

      .platform-btn {
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 0;
        width: 53px;
        height: 16px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;

        &.aos-btn {
          background-image: url(@/assets/imgs/p2/aos.png);

          &.active {
            background-image: url(@/assets/imgs/p2/aos-active.png);
          }
        }

        &.ios-btn {
          background-image: url(@/assets/imgs/p2/ios.png);

          &.active {
            background-image: url(@/assets/imgs/p2/ios-active.png);
          }
        }
      }
    }

    .phone-input {
      position: absolute;
      top: 500px;
      left: 170px;
      width: 420px;
    }

    .rule-btn {
      position: absolute;
      top: 556px;
      left: 540px;
      cursor: pointer;
    }

    .reservation-btn {
      position: absolute;
      bottom: 85px;
      left: 235px;
      cursor: pointer;
    }

    .big-reward {
      position: absolute;
      top: 110px;
      right: 70px;
    }

    .store-buttons {
      position: absolute;
      right: 135px;
      top: 375px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      .store-button {
        width: 190px;
        cursor: pointer;
      }
    }

    .community-btn {
      position: absolute;
      bottom: 85px;
      right: 180px;
      cursor: pointer;
    }
  }
}

.slide-3 {
  .title {
    position: absolute;
    top: 10px;
    z-index: 1;
  }

  .lottery {
    position: absolute;
    top: 10px;
    width: 1920px;

    .lottery-btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
      z-index: 1;
    }
  }

  .rule-btn {
    position: absolute;
    bottom: 50px;
    cursor: pointer;
  }
}

// 第4屏
.slide-4 {
  .title {
    position: absolute;
    top: 10px;
  }

  .reservation-count {
    position: absolute;
    top: 220px;
    display: flex;
    align-items: center;
    justify-content: center;

    .count {
      margin-left: 10px;
      font-size: 60px;
      font-weight: bold;
      background: linear-gradient(180deg, #fcfdfd 0%, #fefece 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: 2px;
      position: relative;
      filter: drop-shadow(0 0 5px #333);

      /* 为不支持背景裁剪的浏览器提供备用方案 */
      color: #fcfdfd;
    }
  }

  .milestone-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin: 200px 0 0 20px;

    .milestone-image {
      position: relative;

      .prize-image {
        position: absolute;
        top: 240px;
        right: 25px;
        cursor: pointer;
      }
    }
  }
}

// 第5屏
.slide-5 {}

// 第7屏
.slide-7 {
  .world-view-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 0;
    box-sizing: border-box;
    position: relative;

    .tab-navigation {
      display: flex;
      gap: 2rem;
      margin: 2rem 0;
      z-index: 100;
      position: absolute;
      top: 2rem;
      left: 50%;
      transform: translateX(-50%);

      .tab-btn {
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        background-repeat: no-repeat;
        background-position: center;
        width: 351px;
        height: 89px;

        .active {
          width: 345px;
          height: 184px;
        }

        &:hover {
          transform: scale(1.05);
          filter: brightness(1.1);
        }

        &.tab1 {
          background-image: url(@/assets/imgs/p7/tab1.png);

          &.active {
            background-image: url(@/assets/imgs/p7/tab1-active.png);
          }
        }

        &.tab2 {
          background-image: url(@/assets/imgs/p7/tab2.png);

          &.active {
            background-image: url(@/assets/imgs/p7/tab2-active.png);
          }
        }
      }
    }

    .tab-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }

    // Tab Swiper 样式
    .tab-swiper {
      width: 100%;
      height: 100%;

      .swiper-wrapper {
        width: 100%;
        height: 100%;
      }

      .swiper-slide {
        width: 100%;
        height: 100%;
        position: relative;
      }
    }

    .tab-panel {
      width: 100%;
      height: 100%;
      position: relative;

      // Tab1 - 水平全屏轮播样式
      &.tab1-panel {
        // 确保Tab1面板占据整个第7屏
        width: 100vw;
        height: 100vh;
        z-index: 10;
      }

      // Tab2 - 固定背景样式
      &.tab2-panel {
        background-image: url('@/assets/imgs/bg/p7-2.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100vh;
        z-index: 10;
      }
    }
  }
}
</style>