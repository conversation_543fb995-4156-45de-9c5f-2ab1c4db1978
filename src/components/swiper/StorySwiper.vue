<template>
  <div class="story-swiper-container">
    <BaseSwiper slides-per-view="auto" :centered-slides="true" :speed="800" effect="coverflow"
      :coverflow-effect="{ slideShadows: false, stretch: '20%', depth: 200, rotate: 0, }" :navigation="true"
      :modules="modules" custom-class="story-swiper">
      <swiper-slide v-for="(story, index) in features" :key="index" class="story-slide">
        <img :src="story.image" />
      </swiper-slide>
    </BaseSwiper>
  </div>
</template>

<script setup lang="ts">
import { SwiperSlide } from 'swiper/vue'
import BaseSwiper from './BaseSwiper.vue'
import { useSwiper } from '@/hooks/useSwiper'
import { Navigation, Autoplay, EffectCoverflow } from 'swiper/modules'

import feature1 from '@/assets/imgs/p7/feature1.jpg'
import feature2 from '@/assets/imgs/p7/feature2.jpg'
import feature3 from '@/assets/imgs/p7/feature3.jpg'
import feature4 from '@/assets/imgs/p7/feature4.jpg'
import feature5 from '@/assets/imgs/p7/feature5.jpg'


const emit = defineEmits<{
  slideChange: [index: number]
  swiperReady: [swiper: any]
}>()

// 使用基础 Swiper Hook
const {
  modules
} = useSwiper([Navigation, Autoplay, EffectCoverflow])

const features = [
  { image: feature1 },
  { image: feature2 },
  { image: feature3 },
  { image: feature4 },
  { image: feature5 }
]
</script>

<style lang="less" scoped>
.story-swiper-container {
  position: relative;
  width: 1300px;
  margin-top: 120px; // 为顶部Tab按钮留出空间
  z-index: 2;
}

.story-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1036px;
  height: 588px;
  background: url(@/assets/imgs/p7/feature-bg.png) no-repeat;

  img {
    width: 95%;
    height: 530px;
  }
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  width: 83px;
  height: 85px;
  z-index: 50;
  background: url(@/assets/imgs/p7/tab2-arrow.png);

  &:hover {
    background: url(@/assets/imgs/p7/tab2-arrow.png);
  }

  &:after {
    display: none;
  }
}

:deep(.swiper-button-prev) {
  transform: rotate(180deg);
  left: 0;
}

:deep(.swiper-button-next) {
  right: 0;
}
</style>
