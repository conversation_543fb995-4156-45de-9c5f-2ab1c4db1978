<template>
  <div class="vertical-swiper-container">
    <BaseSwiper :initial-slide="4" direction="vertical" :slides-per-view="1" :space-between="0" :speed="500"
      :mousewheel="true" :keyboard="{ enabled: true }" :modules="modules" :custom-class="props.customClass"
      @swiper-ready="onSwiperReady" @slide-change="onSlideChange">
      <slot />
    </BaseSwiper>
  </div>
</template>

<script setup lang="ts">

import BaseSwiper from './BaseSwiper.vue'
import { useSwiper } from '@/hooks/useSwiper'
import { Navigation, Pagination, Mousewheel, Keyboard } from 'swiper/modules'

interface Props {
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  customClass: '',
  showNavigation: true
})

const emit = defineEmits<{
  slideChange: [index: number]
  swiperReady: [swiper: any]
}>()

// 使用基础 Swiper Hook
const {
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  modules
} = useSwiper([Navigation, Pagination, Mousewheel, Keyboard])

// Swiper 初始化回调
const onSwiperReady = (swiper: any) => {
  emit('swiperReady', swiper)
}

// 滑动变化回调
const onSlideChange = (swiper: any) => {
  emit('slideChange', swiper.activeIndex)
}

// 暴露方法给父组件
defineExpose({
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide
})
</script>

<style lang="less" scoped>
.vertical-swiper-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.vertical-swiper {
  width: 100%;
  height: 100vh;
}

// 导航指示器样式
.navigation-dots {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .nav-dot {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.5);
      transform: scale(1.1);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      border-color: white;
      transform: scale(1.2);
    }
  }

  @media (max-width: 768px) {
    right: 15px;

    .nav-dot {
      width: 40px;
      height: 40px;
      font-size: 0.9rem;
    }
  }
}

// Swiper 自定义样式
:deep(.swiper-pagination-bullet) {
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;

  &.swiper-pagination-bullet-active {
    background: white;
  }
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  color: white;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;

  &:after {
    font-size: 20px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>
