import { ref } from 'vue'

export function useDeviceDetection() {
  const isIOS = ref(false)
  const isAndroid = ref(false)
  const deviceType = ref<'ios' | 'android' | 'other'>('other')

  const detectDevice = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    
    // 检测iOS设备
    const iosDevices = /iphone|ipad|ipod/i
    const isIOSDevice = iosDevices.test(userAgent)
    
    // 检测Android设备  
    const androidDevices = /android/i
    const isAndroidDevice = androidDevices.test(userAgent)
    
    isIOS.value = isIOSDevice
    isAndroid.value = isAndroidDevice
    
    if (isIOSDevice) {
      deviceType.value = 'ios'
    } else if (isAndroidDevice) {
      deviceType.value = 'android'
    } else {
      deviceType.value = 'other'
    }
  }

  // 初始化时检测设备
  detectDevice()

  return {
    isIOS,
    isAndroid,
    deviceType,
    detectDevice
  }
}